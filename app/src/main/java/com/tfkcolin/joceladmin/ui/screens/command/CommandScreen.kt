package com.tfkcolin.joceladmin.ui.screens.command

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.FilterList
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.tfkcolin.joceladmin.data.models.Command
import com.tfkcolin.joceladmin.data.models.CommandStatus

/**
 * Command list screen
 * Displays list of commands with filtering and search capabilities
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CommandScreen(
    onNavigateToDetails: (String) -> Unit,
    onNavigateToCreate: () -> Unit,
    viewModel: CommandListViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    var showFilterDialog by remember { mutableStateOf(false) }
    var searchQuery by remember { mutableStateOf("") }

    LaunchedEffect(searchQuery) {
        viewModel.searchCommands(searchQuery)
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Header with title and actions
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Commands",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold
            )
            
            Row {
                IconButton(onClick = { showFilterDialog = true }) {
                    Icon(Icons.Default.FilterList, contentDescription = "Filter")
                }
                
                FloatingActionButton(
                    onClick = onNavigateToCreate,
                    modifier = Modifier.size(48.dp)
                ) {
                    Icon(Icons.Default.Add, contentDescription = "Add Command")
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Search bar
        OutlinedTextField(
            value = searchQuery,
            onValueChange = { searchQuery = it },
            label = { Text("Search by client name") },
            leadingIcon = { Icon(Icons.Default.Search, contentDescription = null) },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )

        Spacer(modifier = Modifier.height(16.dp))

        // Statistics cards
        if (uiState.statistics.isNotEmpty()) {
            CommandStatisticsRow(statistics = uiState.statistics)
            Spacer(modifier = Modifier.height(16.dp))
        }

        // Active filters display
        if (uiState.selectedStatus != null || uiState.selectedCountry.isNotBlank()) {
            ActiveFiltersRow(
                selectedStatus = uiState.selectedStatus,
                selectedCountry = uiState.selectedCountry,
                onClearFilters = viewModel::clearFilters
            )
            Spacer(modifier = Modifier.height(8.dp))
        }

        // Commands list
        when {
            uiState.isLoading -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            }
            
            uiState.errorMessage != null -> {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.errorContainer
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "Error",
                            style = MaterialTheme.typography.titleMedium,
                            color = MaterialTheme.colorScheme.onErrorContainer
                        )
                        Text(
                            text = uiState.errorMessage,
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onErrorContainer
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Button(
                            onClick = {
                                viewModel.clearError()
                                viewModel.refresh()
                            }
                        ) {
                            Text("Retry")
                        }
                    }
                }
            }
            
            uiState.commands.isEmpty() -> {
                Card(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(32.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "No commands found",
                            style = MaterialTheme.typography.titleMedium
                        )
                        Text(
                            text = "Create your first command to get started",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Button(onClick = onNavigateToCreate) {
                            Text("Create Command")
                        }
                    }
                }
            }
            
            else -> {
                LazyColumn(
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(uiState.commands) { command ->
                        CommandItem(
                            command = command,
                            onClick = { onNavigateToDetails(command.id) },
                            onStatusUpdate = { status ->
                                viewModel.updateCommandStatus(command.id, status)
                            }
                        )
                    }
                }
            }
        }
    }

    // Filter dialog
    if (showFilterDialog) {
        CommandFilterDialog(
            selectedStatus = uiState.selectedStatus,
            selectedCountry = uiState.selectedCountry,
            onStatusSelected = { status ->
                viewModel.filterByStatus(status)
                showFilterDialog = false
            },
            onCountrySelected = { country ->
                viewModel.filterByCountry(country)
                showFilterDialog = false
            },
            onDismiss = { showFilterDialog = false }
        )
    }
}

@Composable
private fun CommandStatisticsRow(
    statistics: Map<String, Int>
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        StatisticCard(
            title = "Total",
            value = statistics["total"] ?: 0,
            modifier = Modifier.weight(1f)
        )
        StatisticCard(
            title = "Completed",
            value = statistics["completed"] ?: 0,
            modifier = Modifier.weight(1f)
        )
        StatisticCard(
            title = "Pending",
            value = statistics["pending"] ?: 0,
            modifier = Modifier.weight(1f)
        )
    }
}

@Composable
private fun StatisticCard(
    title: String,
    value: Int,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
    ) {
        Column(
            modifier = Modifier.padding(12.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = value.toString(),
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = title,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun ActiveFiltersRow(
    selectedStatus: CommandStatus?,
    selectedCountry: String,
    onClearFilters: () -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = "Active filters:",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        if (selectedStatus != null) {
            FilterChip(
                selected = true,
                onClick = onClearFilters,
                label = { Text(selectedStatus.label) }
            )
        }
        
        if (selectedCountry.isNotBlank()) {
            FilterChip(
                selected = true,
                onClick = onClearFilters,
                label = { Text(selectedCountry) }
            )
        }
        
        TextButton(onClick = onClearFilters) {
            Text("Clear all")
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun CommandItem(
    command: Command,
    onClick: () -> Unit,
    onStatusUpdate: (CommandStatus) -> Unit
) {
    Card(
        onClick = onClick,
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // Header row with command number and status
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = command.getFormattedCommandNumber(),
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )

                CommandStatusChip(
                    status = command.getStatus(),
                    onClick = { /* TODO: Status update dialog */ }
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            // Client information
            Text(
                text = command.client.getDisplayName(),
                style = MaterialTheme.typography.bodyLarge
            )

            if (command.client.getFormattedLocation().isNotBlank()) {
                Text(
                    text = command.client.getFormattedLocation(),
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            // Products summary
            Text(
                text = "${command.products.size} product(s)",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            // Total amount
            Text(
                text = command.getFormattedTotalAmount(),
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.primary
            )
        }
    }
}

@Composable
private fun CommandStatusChip(
    status: CommandStatus,
    onClick: () -> Unit
) {
    FilterChip(
        selected = true,
        onClick = onClick,
        label = { Text(status.label) },
        colors = FilterChipDefaults.filterChipColors(
            selectedContainerColor = when (status) {
                CommandStatus.RECORD -> MaterialTheme.colorScheme.surfaceVariant
                CommandStatus.BUYING -> MaterialTheme.colorScheme.primaryContainer
                CommandStatus.RECEIVED -> MaterialTheme.colorScheme.secondaryContainer
                CommandStatus.DELIVERED -> MaterialTheme.colorScheme.tertiaryContainer
                CommandStatus.READY -> MaterialTheme.colorScheme.surfaceVariant
                CommandStatus.OK -> MaterialTheme.colorScheme.primaryContainer
            }
        )
    )
}

@Composable
private fun CommandFilterDialog(
    selectedStatus: CommandStatus?,
    selectedCountry: String,
    onStatusSelected: (CommandStatus?) -> Unit,
    onCountrySelected: (String) -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Filter Commands") },
        text = {
            Column {
                Text(
                    text = "Filter by Status",
                    style = MaterialTheme.typography.titleSmall,
                    modifier = Modifier.padding(bottom = 8.dp)
                )

                // Status filter options
                CommandStatus.values().forEach { status ->
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = selectedStatus == status,
                            onClick = { onStatusSelected(status) }
                        )
                        Text(
                            text = status.label,
                            modifier = Modifier.padding(start = 8.dp)
                        )
                    }
                }

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = selectedStatus == null,
                        onClick = { onStatusSelected(null) }
                    )
                    Text(
                        text = "All Statuses",
                        modifier = Modifier.padding(start = 8.dp)
                    )
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("Close")
            }
        }
    )
}
