package com.tfkcolin.joceladmin.data.models

import kotlinx.serialization.Serializable
import java.util.Calendar
import java.util.Date

/**
 * Command data model representing customer orders
 * Implements the 6-stage workflow: Record → Buying → Received → Delivered → Ready → OK
 */
@Serializable
data class Command(
    val id: String = "",
    val commandNumber: String = "",
    val client: ClientData = ClientData(),
    val products: List<MiniProduct> = emptyList(),
    val commandStepIndex: Int = 0, // Maps to CommandStatus ordinal
    val observation: List<String> = emptyList(),
    val paymentProofImageUrl: String? = null,
    val proofUploaded: Boolean = false,
    val created: Long = Calendar.getInstance().timeInMillis,
    val updatedAt: Long = System.currentTimeMillis(),
    val createdBy: String = "",
    val assignedTo: String? = null,
    val country: String = "",
    val currency: String = "USD",
    val estimatedDeliveryDate: Long? = null,
    val actualDeliveryDate: Long? = null,
    val totalSellingAmount: Double = 0.0,
    val totalBuyingAmount: Double = 0.0,
    val isArchived: Boolean = false
) {
    /**
     * Get current command status
     */
    fun getStatus(): CommandStatus {
        return CommandStatus.values().getOrNull(commandStepIndex) ?: CommandStatus.RECORD
    }

    /**
     * Update command status
     */
    fun updateStatus(status: CommandStatus): Command {
        return copy(
            commandStepIndex = status.ordinal,
            updatedAt = System.currentTimeMillis()
        )
    }

    /**
     * Calculate total selling amount from products
     */
    fun calculateTotalSellingAmount(): Double {
        return products.sumOf { it.getTotalSellingPrice() }
    }

    /**
     * Calculate total buying amount from products
     */
    fun calculateTotalBuyingAmount(): Double {
        return products.sumOf { it.getTotalBuyingPrice() }
    }

    /**
     * Calculate profit margin
     */
    fun getProfitMargin(): Double {
        return calculateTotalSellingAmount() - calculateTotalBuyingAmount()
    }

    /**
     * Calculate profit percentage
     */
    fun getProfitPercentage(): Double {
        val buyingAmount = calculateTotalBuyingAmount()
        return if (buyingAmount > 0) {
            (getProfitMargin() / buyingAmount) * 100
        } else 0.0
    }

    /**
     * Check if command is completed
     */
    fun isCompleted(): Boolean {
        return getStatus() == CommandStatus.OK
    }

    /**
     * Check if payment proof is required
     */
    fun requiresPaymentProof(): Boolean {
        return getStatus().ordinal >= CommandStatus.READY.ordinal && !proofUploaded
    }

    /**
     * Add observation note
     */
    fun addObservation(note: String): Command {
        return copy(
            observation = observation + note,
            updatedAt = System.currentTimeMillis()
        )
    }

    /**
     * Update payment proof
     */
    fun updatePaymentProof(imageUrl: String): Command {
        return copy(
            paymentProofImageUrl = imageUrl,
            proofUploaded = true,
            updatedAt = System.currentTimeMillis()
        )
    }

    /**
     * Get formatted command number
     */
    fun getFormattedCommandNumber(): String {
        return if (commandNumber.isNotBlank()) commandNumber else "CMD-${id.take(8)}"
    }

    /**
     * Get command summary for display
     */
    fun getSummary(): String {
        return "${getFormattedCommandNumber()} - ${client.getDisplayName()}"
    }

    /**
     * Validate command data
     */
    fun isValid(): Boolean {
        return client.isValid() && products.isNotEmpty() && products.all { it.isValid() }
    }

    /**
     * Get formatted total amount
     */
    fun getFormattedTotalAmount(): String {
        return "$currency ${String.format("%.2f", calculateTotalSellingAmount())}"
    }

    /**
     * Check if command can progress to next status
     */
    fun canProgress(): Boolean {
        return getStatus().canProgress() && isValid()
    }

    /**
     * Check if command can regress to previous status
     */
    fun canRegress(): Boolean {
        return getStatus().canRegress()
    }
}

/**
 * Client data for commands
 */
@Serializable
data class ClientData(
    val name: String = "",
    val email: String = "",
    val phone: String = "",
    val address: String = "",
    val city: String = "",
    val country: String = "",
    val postalCode: String = "",
    val additionalInfo: String = ""
)

/**
 * Product information within a command
 */
@Serializable
data class MiniProduct(
    val id: String = "",
    val name: String = "",
    val description: String = "",
    val quantity: Int = 1,
    val unitPrice: Double = 0.0,
    val totalPrice: Double = 0.0,
    val imageUrl: String? = null,
    val category: String = "",
    val weight: Double? = null,
    val dimensions: String? = null,
    val sku: String? = null
)

/**
 * Command status representing the 6-stage workflow
 */
@Serializable
enum class CommandStatus(val displayName: String, val order: Int) {
    RECORD("Record", 1),
    BUYING("Buying", 2),
    RECEIVED("Received", 3),
    DELIVERED("Delivered", 4),
    READY("Ready", 5),
    OK("OK", 6);
    
    /**
     * Get the next status in the workflow
     */
    fun getNext(): CommandStatus? {
        return values().find { it.order == this.order + 1 }
    }
    
    /**
     * Get the previous status in the workflow
     */
    fun getPrevious(): CommandStatus? {
        return values().find { it.order == this.order - 1 }
    }
    
    /**
     * Check if this status can transition to another status
     */
    fun canTransitionTo(newStatus: CommandStatus): Boolean {
        return when {
            newStatus.order == this.order + 1 -> true // Next step
            newStatus.order == this.order - 1 -> true // Previous step (rollback)
            newStatus == this -> true // Same status (update)
            else -> false
        }
    }
}

/**
 * Command statistics for dashboard
 */
@Serializable
data class CommandStats(
    val totalCommands: Int = 0,
    val commandsByStatus: Map<CommandStatus, Int> = emptyMap(),
    val totalValue: Double = 0.0,
    val averageValue: Double = 0.0,
    val completedThisMonth: Int = 0,
    val pendingCommands: Int = 0
)
