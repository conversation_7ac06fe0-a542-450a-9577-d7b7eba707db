package com.tfkcolin.joceladmin.data.models

import kotlinx.serialization.Serializable

/**
 * Command data model representing customer orders
 * Implements the 6-stage workflow: Record → Buying → Received → Delivered → Ready → OK
 */
@Serializable
data class Command(
    val id: String = "",
    val commandNumber: String = "",
    val clientData: ClientData = ClientData(),
    val products: List<MiniProduct> = emptyList(),
    val status: CommandStatus = CommandStatus.RECORD,
    val totalAmount: Double = 0.0,
    val currency: String = "USD",
    val paymentProof: String? = null,
    val notes: String = "",
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
    val createdBy: String = "",
    val assignedTo: String? = null,
    val country: String = "",
    val estimatedDeliveryDate: Long? = null,
    val actualDeliveryDate: Long? = null
)

/**
 * Client data for commands
 */
@Serializable
data class ClientData(
    val name: String = "",
    val email: String = "",
    val phone: String = "",
    val address: String = "",
    val city: String = "",
    val country: String = "",
    val postalCode: String = "",
    val additionalInfo: String = ""
)

/**
 * Product information within a command
 */
@Serializable
data class MiniProduct(
    val id: String = "",
    val name: String = "",
    val description: String = "",
    val quantity: Int = 1,
    val unitPrice: Double = 0.0,
    val totalPrice: Double = 0.0,
    val imageUrl: String? = null,
    val category: String = "",
    val weight: Double? = null,
    val dimensions: String? = null,
    val sku: String? = null
)

/**
 * Command status representing the 6-stage workflow
 */
@Serializable
enum class CommandStatus(val displayName: String, val order: Int) {
    RECORD("Record", 1),
    BUYING("Buying", 2),
    RECEIVED("Received", 3),
    DELIVERED("Delivered", 4),
    READY("Ready", 5),
    OK("OK", 6);
    
    /**
     * Get the next status in the workflow
     */
    fun getNext(): CommandStatus? {
        return values().find { it.order == this.order + 1 }
    }
    
    /**
     * Get the previous status in the workflow
     */
    fun getPrevious(): CommandStatus? {
        return values().find { it.order == this.order - 1 }
    }
    
    /**
     * Check if this status can transition to another status
     */
    fun canTransitionTo(newStatus: CommandStatus): Boolean {
        return when {
            newStatus.order == this.order + 1 -> true // Next step
            newStatus.order == this.order - 1 -> true // Previous step (rollback)
            newStatus == this -> true // Same status (update)
            else -> false
        }
    }
}

/**
 * Command statistics for dashboard
 */
@Serializable
data class CommandStats(
    val totalCommands: Int = 0,
    val commandsByStatus: Map<CommandStatus, Int> = emptyMap(),
    val totalValue: Double = 0.0,
    val averageValue: Double = 0.0,
    val completedThisMonth: Int = 0,
    val pendingCommands: Int = 0
)
