package com.tfkcolin.joceladmin.data.models

import kotlinx.serialization.Serializable

/**
 * Financial transaction data model for multi-country financial management
 */
@Serializable
data class FinancialTransaction(
    val id: String = "",
    val transactionNumber: String = "",
    val type: TransactionType = TransactionType.INCOME,
    val category: String = "",
    val amount: Double = 0.0,
    val currency: String = "USD",
    val exchangeRate: Double = 1.0,
    val amountInBaseCurrency: Double = 0.0,
    val description: String = "",
    val country: String = "",
    val commandId: String? = null, // Link to command if applicable
    val cargoId: String? = null, // Link to cargo if applicable
    val date: Long = System.currentTimeMillis(),
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
    val createdBy: String = "",
    val approvedBy: String? = null,
    val status: TransactionStatus = TransactionStatus.PENDING,
    val paymentMethod: String = "",
    val reference: String = "",
    val attachments: List<String> = emptyList(),
    val tags: List<String> = emptyList(),
    val notes: String = ""
)

/**
 * Transaction types
 */
@Serializable
enum class TransactionType(val displayName: String) {
    INCOME("Income"),
    EXPENSE("Expense"),
    TRANSFER("Transfer"),
    REFUND("Refund"),
    FEE("Fee"),
    TAX("Tax")
}

/**
 * Transaction status
 */
@Serializable
enum class TransactionStatus(val displayName: String) {
    PENDING("Pending"),
    APPROVED("Approved"),
    REJECTED("Rejected"),
    COMPLETED("Completed"),
    CANCELLED("Cancelled")
}

/**
 * Financial summary for a specific period and country
 */
@Serializable
data class FinancialSummary(
    val country: String = "",
    val currency: String = "USD",
    val period: String = "", // e.g., "2024-12", "2024-Q4"
    val totalIncome: Double = 0.0,
    val totalExpenses: Double = 0.0,
    val netProfit: Double = 0.0,
    val transactionCount: Int = 0,
    val incomeByCategory: Map<String, Double> = emptyMap(),
    val expensesByCategory: Map<String, Double> = emptyMap(),
    val monthlyTrend: List<MonthlyData> = emptyList()
)

/**
 * Monthly financial data for trends
 */
@Serializable
data class MonthlyData(
    val month: String = "", // e.g., "2024-12"
    val income: Double = 0.0,
    val expenses: Double = 0.0,
    val profit: Double = 0.0,
    val transactionCount: Int = 0
)

/**
 * Country financial data
 */
@Serializable
data class CountryData(
    val id: String = "",
    val name: String = "",
    val code: String = "", // ISO country code
    val currency: String = "USD",
    val exchangeRate: Double = 1.0,
    val isActive: Boolean = true,
    val taxRate: Double = 0.0,
    val bankingInfo: BankingInfo = BankingInfo(),
    val regulations: List<String> = emptyList()
)

/**
 * Banking information for a country
 */
@Serializable
data class BankingInfo(
    val bankName: String = "",
    val accountNumber: String = "",
    val routingNumber: String = "",
    val swiftCode: String = "",
    val iban: String = "",
    val address: String = ""
)

/**
 * Financial dashboard statistics
 */
@Serializable
data class FinancialStats(
    val totalRevenue: Double = 0.0,
    val totalExpenses: Double = 0.0,
    val netProfit: Double = 0.0,
    val profitMargin: Double = 0.0,
    val revenueGrowth: Double = 0.0,
    val expenseGrowth: Double = 0.0,
    val transactionCount: Int = 0,
    val averageTransactionValue: Double = 0.0,
    val topCategories: List<CategorySummary> = emptyList(),
    val countryBreakdown: List<CountrySummary> = emptyList()
)

/**
 * Category summary for financial analysis
 */
@Serializable
data class CategorySummary(
    val category: String = "",
    val amount: Double = 0.0,
    val percentage: Double = 0.0,
    val transactionCount: Int = 0
)

/**
 * Country summary for financial analysis
 */
@Serializable
data class CountrySummary(
    val country: String = "",
    val revenue: Double = 0.0,
    val expenses: Double = 0.0,
    val profit: Double = 0.0,
    val transactionCount: Int = 0
)
