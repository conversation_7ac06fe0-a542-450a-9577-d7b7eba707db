package com.tfkcolin.joceladmin.data.models

import kotlinx.serialization.Serializable
import java.util.UUID

/**
 * Product within a command
 * Represents individual products in customer orders
 */
@Serializable
data class MiniProduct(
    val id: String = UUID.randomUUID().toString(),
    val name: String = "",
    val quantity: Int = 0,
    val unitSellingPrice: Double = 0.0,
    val unitBuyingPrice: Double = 0.0,
    val description: String = "",
    val productEvolutionStep: String = CommandStatus.RECORD.step,
    val productImage: String? = null,
    val previewsPathName: String = UUID.randomUUID().toString(),
    val soldOut: Boolean = false,
    val category: String = "",
    val weight: Double = 0.0,
    val dimensions: String = ""
) {
    /**
     * Calculate total selling price
     */
    fun getTotalSellingPrice(): Double {
        return quantity * unitSellingPrice
    }

    /**
     * Calculate total buying price
     */
    fun getTotalBuyingPrice(): Double {
        return quantity * unitBuyingPrice
    }

    /**
     * Calculate profit margin
     */
    fun getProfitMargin(): Double {
        return getTotalSellingPrice() - getTotalBuyingPrice()
    }

    /**
     * Calculate profit percentage
     */
    fun getProfitPercentage(): Double {
        return if (getTotalBuyingPrice() > 0) {
            (getProfitMargin() / getTotalBuyingPrice()) * 100
        } else 0.0
    }

    /**
     * Validate product data
     */
    fun isValid(): Boolean {
        return name.isNotBlank() && 
               quantity > 0 && 
               unitSellingPrice >= 0 && 
               unitBuyingPrice >= 0
    }

    /**
     * Check if product is profitable
     */
    fun isProfitable(): Boolean {
        return unitSellingPrice > unitBuyingPrice
    }

    /**
     * Get product status from evolution step
     */
    fun getStatus(): CommandStatus {
        return CommandStatus.fromStep(productEvolutionStep)
    }

    /**
     * Update product evolution step
     */
    fun updateStatus(status: CommandStatus): MiniProduct {
        return copy(productEvolutionStep = status.step)
    }

    /**
     * Get formatted price display
     */
    fun getFormattedSellingPrice(currency: String = "USD"): String {
        return "$currency ${String.format("%.2f", getTotalSellingPrice())}"
    }

    /**
     * Get formatted buying price display
     */
    fun getFormattedBuyingPrice(currency: String = "USD"): String {
        return "$currency ${String.format("%.2f", getTotalBuyingPrice())}"
    }

    /**
     * Get product summary for display
     */
    fun getSummary(): String {
        return "$name (Qty: $quantity)"
    }
}
