package com.tfkcolin.joceladmin.data.models

import kotlinx.serialization.Serializable

/**
 * Cargo data model representing shipping containers
 * Implements cargo workflow: Loading → In Transit → Arrived → Delivered
 */
@Serializable
data class Cargo(
    val id: String = "",
    val cargoNumber: String = "",
    val name: String = "",
    val description: String = "",
    val origin: Location = Location(),
    val destination: Location = Location(),
    val status: CargoStatus = CargoStatus.LOADING,
    val shipments: List<String> = emptyList(), // Shipment IDs
    val totalWeight: Double = 0.0,
    val totalVolume: Double = 0.0,
    val maxWeight: Double = 0.0,
    val maxVolume: Double = 0.0,
    val estimatedDepartureDate: Long? = null,
    val actualDepartureDate: Long? = null,
    val estimatedArrivalDate: Long? = null,
    val actualArrivalDate: Long? = null,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
    val createdBy: String = "",
    val notes: String = "",
    val trackingNumber: String? = null,
    val carrier: String = "",
    val route: List<Location> = emptyList()
)

/**
 * Shipment data model representing individual customer shipments within cargos
 */
@Serializable
data class Shipment(
    val id: String = "",
    val shipmentNumber: String = "",
    val cargoId: String = "",
    val commandId: String? = null, // Link to command if applicable
    val clientData: ClientData = ClientData(),
    val products: List<ShipmentProduct> = emptyList(),
    val status: ShipmentStatus = ShipmentStatus.PENDING,
    val weight: Double = 0.0,
    val volume: Double = 0.0,
    val value: Double = 0.0,
    val currency: String = "USD",
    val origin: Location = Location(),
    val destination: Location = Location(),
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
    val deliveredAt: Long? = null,
    val notes: String = "",
    val trackingNumber: String? = null
)

/**
 * Product information within a shipment
 */
@Serializable
data class ShipmentProduct(
    val id: String = "",
    val name: String = "",
    val description: String = "",
    val quantity: Int = 1,
    val weight: Double = 0.0,
    val volume: Double = 0.0,
    val value: Double = 0.0,
    val imageUrl: String? = null,
    val category: String = "",
    val sku: String? = null,
    val customsCode: String? = null
)

/**
 * Location data for origin/destination tracking
 */
@Serializable
data class Location(
    val name: String = "",
    val address: String = "",
    val city: String = "",
    val country: String = "",
    val postalCode: String = "",
    val latitude: Double? = null,
    val longitude: Double? = null,
    val port: String? = null,
    val airport: String? = null
)

/**
 * Cargo status workflow
 */
@Serializable
enum class CargoStatus(val displayName: String, val order: Int) {
    LOADING("Loading", 1),
    IN_TRANSIT("In Transit", 2),
    ARRIVED("Arrived", 3),
    DELIVERED("Delivered", 4);
    
    fun getNext(): CargoStatus? {
        return values().find { it.order == this.order + 1 }
    }
    
    fun getPrevious(): CargoStatus? {
        return values().find { it.order == this.order - 1 }
    }
}

/**
 * Shipment status workflow
 */
@Serializable
enum class ShipmentStatus(val displayName: String, val order: Int) {
    PENDING("Pending", 1),
    LOADED("Loaded", 2),
    IN_TRANSIT("In Transit", 3),
    DELIVERED("Delivered", 4);
    
    fun getNext(): ShipmentStatus? {
        return values().find { it.order == this.order + 1 }
    }
    
    fun getPrevious(): ShipmentStatus? {
        return values().find { it.order == this.order - 1 }
    }
}

/**
 * Cargo statistics for dashboard
 */
@Serializable
data class CargoStats(
    val totalCargos: Int = 0,
    val cargosByStatus: Map<CargoStatus, Int> = emptyMap(),
    val totalShipments: Int = 0,
    val shipmentsByStatus: Map<ShipmentStatus, Int> = emptyMap(),
    val totalWeight: Double = 0.0,
    val totalVolume: Double = 0.0,
    val averageDeliveryTime: Double = 0.0
)
