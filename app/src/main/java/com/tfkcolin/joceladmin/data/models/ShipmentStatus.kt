package com.tfkcolin.joceladmin.data.models

/**
 * Shipment status enumeration
 * Represents the 4-stage shipment workflow: Pending → Loaded → In Transit → Delivered
 */
enum class ShipmentStatus(
    val label: String,
    val description: String
) {
    PENDING(
        label = "Pending",
        description = "Shipment awaiting cargo assignment"
    ),
    LOADED(
        label = "Loaded",
        description = "Shipment loaded into cargo container"
    ),
    IN_TRANSIT(
        label = "In Transit",
        description = "Shipment en route with cargo"
    ),
    DELIVERED(
        label = "Delivered",
        description = "Shipment delivered to customer"
    );

    companion object {
        /**
         * Get status by ordinal index
         */
        fun fromIndex(index: Int): ShipmentStatus {
            return values().getOrNull(index) ?: PENDING
        }

        /**
         * Get next status in workflow
         */
        fun ShipmentStatus.getNext(): ShipmentStatus? {
            val currentIndex = values().indexOf(this)
            return if (currentIndex < values().size - 1) {
                values()[currentIndex + 1]
            } else null
        }

        /**
         * Get previous status in workflow
         */
        fun ShipmentStatus.getPrevious(): ShipmentStatus? {
            val currentIndex = values().indexOf(this)
            return if (currentIndex > 0) {
                values()[currentIndex - 1]
            } else null
        }

        /**
         * Check if status can progress to next
         */
        fun ShipmentStatus.canProgress(): Boolean {
            return this != DELIVERED
        }

        /**
         * Check if status can regress to previous
         */
        fun ShipmentStatus.canRegress(): Boolean {
            return this != PENDING
        }
    }
}
